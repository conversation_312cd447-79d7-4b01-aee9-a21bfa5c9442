#!/bin/bash

# Ensure loader is available
EXE_FILE_NAME=${EXE_FILE_NAME:-$(which tsbs_load_ltmdb)}
if [[ -z "$EXE_FILE_NAME" ]]; then
    echo "tsbs_load_ltmdb not available. It is not specified explicitly and not found in \$PATH"
    exit 1
fi

# Load parameters - common
DATA_FILE_NAME=${DATA_FILE_NAME:-ltmdb-data.gz}
DATABASE_USER=${DATABASE_USER:-root}
DATABASE_NAME=${DATABASE_NAME:-benchmark}
DATABASE_HOST=${DATABASE_HOST:-localhost}
DATABASE_PORT=${DATABASE_PORT:-3306}
DATABASE_PWD=${DATABASE_PWD:-""}
ENGINE=${ENGINE:-kmemstore}

EXE_DIR=${EXE_DIR:-$(dirname $0)}
source ${EXE_DIR}/load_common.sh

cat ${DATA_FILE} | gunzip | $EXE_FILE_NAME \
                                --db-name=${DATABASE_NAME} \
                                --host=${DATABASE_HOST} \
                                --port=${DATABASE_PORT} \
                                --pass=${DATABASE_PWD} \
                                --user=${DATABASE_USER} \
                                --workers=${NUM_WORKERS} \
                                --storage-engine=${ENGINE} \
                                --batch-size=${BATCH_SIZE}
