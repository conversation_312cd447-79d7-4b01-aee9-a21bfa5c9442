package query

import (
	"fmt"
	"sync"
)

// DuckDB encodes a DuckDB request. This will be serialized for use
// by the tsbs_run_queries_duckdb program.
type DuckDB struct {
	HumanLabel       []byte
	HumanDescription []byte

	Table    []byte // e.g. "cpu"
	SqlQuery []byte
	id       uint64
}

// DuckDBPool is a sync.Pool of DuckDB Query types
var DuckDBPool = sync.Pool{
	New: func() interface{} {
		return &DuckDB{
			HumanLabel:       make([]byte, 0, 1024),
			HumanDescription: make([]byte, 0, 1024),
			Table:            make([]byte, 0, 1024),
			SqlQuery:         make([]byte, 0, 1024),
		}
	},
}

// NewDuckDB returns a new DuckDB Query instance
func NewDuckDB() *DuckDB {
	return DuckDBPool.Get().(*DuckDB)
}

// GetID returns the ID of this Query
func (q *DuckDB) GetID() uint64 {
	return q.id
}

// SetID sets the ID for this Query
func (q *DuckDB) SetID(n uint64) {
	q.id = n
}

// String produces a debug-ready description of a Query.
func (q *DuckDB) String() string {
	return fmt.Sprintf("HumanLabel: %s, HumanDescription: %s, Table: %s, Query: %s", q.HumanLabel, q.HumanDescription, q.Table, q.SqlQuery)
}

// HumanLabelName returns the human readable name of this Query
func (q *DuckDB) HumanLabelName() []byte {
	return q.HumanLabel
}

// HumanDescriptionName returns the human readable description of this Query
func (q *DuckDB) HumanDescriptionName() []byte {
	return q.HumanDescription
}

// Release resets and returns this Query to its pool
func (q *DuckDB) Release() {
	q.HumanLabel = q.HumanLabel[:0]
	q.HumanDescription = q.HumanDescription[:0]
	q.id = 0

	q.Table = q.Table[:0]
	q.SqlQuery = q.SqlQuery[:0]

	DuckDBPool.Put(q)
}
