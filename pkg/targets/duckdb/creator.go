package duckdb

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/marcboeker/go-duckdb"
	"github.com/timescale/tsbs/pkg/data/usecases/common"
	"github.com/timescale/tsbs/pkg/targets"
)

// dbCreator handles database and table creation for DuckDB
type dbCreator struct {
	opts    *LoadingOptions
	ds      targets.DataSource
	driver  string
	connStr string
	headers *common.GeneratedDataHeaders
}

func (d *dbCreator) Init() {
	// DuckDB doesn't require explicit database creation for file-based databases
	// The database file will be created automatically when we connect
}

func (d *dbCreator) DBExists(dbName string) bool {
	// For file-based DuckDB, we can check if the file exists
	// For simplicity, we'll assume it doesn't exist and let DuckDB handle it
	return false
}

func (d *dbCreator) RemoveOldDB(dbName string) error {
	// For file-based DuckDB, we could remove the file
	// For now, we'll just return nil
	return nil
}

func (d *dbCreator) CreateDB(dbName string) error {
	db, err := sql.Open(d.driver, d.connStr)
	if err != nil {
		return fmt.Errorf("failed to open DuckDB connection: %v", err)
	}
	defer db.Close()

	// Test the connection
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping DuckDB: %v", err)
	}

	// Get headers from data source
	if d.headers == nil {
		d.headers = d.ds.Headers()
	}

	// Create tables if enabled
	if d.opts.CreateMetricsTable {
		// Create tags table
		if err := d.createTagsTable(db); err != nil {
			return fmt.Errorf("failed to create tags table: %v", err)
		}

		// Create measurement tables
		for tableName, columns := range d.headers.FieldKeys {
			if err := d.createMeasurementTable(db, tableName, columns); err != nil {
				return fmt.Errorf("failed to create table %s: %v", tableName, err)
			}
		}
	}

	return nil
}

// createTagsTable creates the tags table similar to TimescaleDB
func (d *dbCreator) createTagsTable(db *sql.DB) error {
	// Drop existing table if it exists
	dropSQL := `DROP TABLE IF EXISTS tags`
	if _, err := db.Exec(dropSQL); err != nil {
		return fmt.Errorf("failed to drop existing tags table: %v", err)
	}

	// Create tags table with columns based on tag keys
	var tagColumnDefs []string
	for i, tagName := range d.headers.TagKeys {
		tagType := "VARCHAR" // Default to VARCHAR for DuckDB
		if i < len(d.headers.TagTypes) {
			// Convert tag type to DuckDB type
			switch d.headers.TagTypes[i] {
			case "string":
				tagType = "VARCHAR"
			case "int":
				tagType = "INTEGER"
			case "float":
				tagType = "DOUBLE"
			default:
				tagType = "VARCHAR"
			}
		}
		tagColumnDefs = append(tagColumnDefs, fmt.Sprintf("%s %s", tagName, tagType))
	}

	// First create sequence
	_, err := db.Exec("CREATE SEQUENCE IF NOT EXISTS tags_id_seq")
	if err != nil {
		return fmt.Errorf("failed to create sequence: %v", err)
	}

	createSQL := fmt.Sprintf(`
		CREATE TABLE tags (
			id INTEGER PRIMARY KEY DEFAULT nextval('tags_id_seq'),
			%s
		)
	`, strings.Join(tagColumnDefs, ",\n\t\t\t"))

	if _, err := db.Exec(createSQL); err != nil {
		return fmt.Errorf("failed to create tags table: %v", err)
	}

	// Create unique index on tag columns
	if len(d.headers.TagKeys) > 0 {
		indexSQL := fmt.Sprintf("CREATE UNIQUE INDEX tags_unique_idx ON tags(%s)",
			strings.Join(d.headers.TagKeys, ", "))
		if _, err := db.Exec(indexSQL); err != nil {
			return fmt.Errorf("failed to create tags index: %v", err)
		}
	}

	log.Println("Created tags table successfully")
	return nil
}

// createMeasurementTable creates a measurement table (e.g., cpu, readings, diagnostics)
func (d *dbCreator) createMeasurementTable(db *sql.DB, tableName string, columns []string) error {
	// Drop existing table if it exists
	dropSQL := fmt.Sprintf("DROP TABLE IF EXISTS %s", tableName)
	if _, err := db.Exec(dropSQL); err != nil {
		return fmt.Errorf("failed to drop existing table %s: %v", tableName, err)
	}

	// Create field column definitions
	var fieldColumnDefs []string
	for _, column := range columns {
		fieldColumnDefs = append(fieldColumnDefs, fmt.Sprintf("%s DOUBLE", column))
	}

	// Create the measurement table
	createSQL := fmt.Sprintf(`
		CREATE TABLE %s (
			time TIMESTAMP,
			tags_id INTEGER,
			%s,
			additional_tags JSON DEFAULT NULL
		)
	`, tableName, strings.Join(fieldColumnDefs, ",\n\t\t\t"))

	if _, err := db.Exec(createSQL); err != nil {
		return fmt.Errorf("failed to create table %s: %v", tableName, err)
	}

	// Create indexes
	// Index on tags_id and time
	indexSQL := fmt.Sprintf("CREATE INDEX %s_tags_time_idx ON %s(tags_id, time DESC)", tableName, tableName)
	if _, err := db.Exec(indexSQL); err != nil {
		return fmt.Errorf("failed to create index on table %s: %v", tableName, err)
	}

	// Index on time
	timeIndexSQL := fmt.Sprintf("CREATE INDEX %s_time_idx ON %s(time DESC)", tableName, tableName)
	if _, err := db.Exec(timeIndexSQL); err != nil {
		return fmt.Errorf("failed to create time index on table %s: %v", tableName, err)
	}

	log.Printf("Created table %s successfully", tableName)
	return nil
}

func (d *dbCreator) PostCreateDB(dbName string) error {
	// Any post-creation setup can go here
	return nil
}

func (d *dbCreator) Close() {
	// Nothing to close for the creator
}
