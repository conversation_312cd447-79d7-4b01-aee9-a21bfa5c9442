package duckdb

import (
	"fmt"
	"io"
	"strconv"
	"strings"

	"github.com/timescale/tsbs/pkg/data"
)

// Serializer writes a Point in a CSV-like format for DuckDB
type Serializer struct{}

// Serialize writes Point data to the given writer, conforming to the
// DuckDB CSV format.
func (s *Serializer) Serialize(p *data.Point, w io.Writer) error {
	buf := make([]byte, 0, 1024)

	// Add timestamp
	buf = append(buf, p.Timestamp().UTC().Format("2006-01-02 15:04:05.999999")...)
	buf = append(buf, ',')

	// Add measurement name
	buf = append(buf, p.MeasurementName()...)
	buf = append(buf, ',')

	// Add tags as JSON-like string
	tagKeys := p.TagKeys()
	tagValues := p.TagValues()
	buf = append(buf, '"')
	buf = append(buf, '{')
	for i, key := range tagKeys {
		if i > 0 {
			buf = append(buf, ',')
		}
		buf = append(buf, '"')
		buf = append(buf, key...)
		buf = append(buf, '"')
		buf = append(buf, ':')
		buf = append(buf, '"')
		switch v := tagValues[i].(type) {
		case string:
			// Escape quotes in string values
			escaped := strings.ReplaceAll(v, `"`, `\"`)
			buf = append(buf, escaped...)
		case []byte:
			escaped := strings.ReplaceAll(string(v), `"`, `\"`)
			buf = append(buf, escaped...)
		default:
			buf = append(buf, fmt.Sprintf("%v", v)...)
		}
		buf = append(buf, '"')
	}
	buf = append(buf, '}')
	buf = append(buf, '"')
	buf = append(buf, ',')

	// Add fields as JSON-like string
	fieldKeys := p.FieldKeys()
	fieldValues := p.FieldValues()
	buf = append(buf, '"')
	buf = append(buf, '{')
	for i, key := range fieldKeys {
		if i > 0 {
			buf = append(buf, ',')
		}
		buf = append(buf, '"')
		buf = append(buf, key...)
		buf = append(buf, '"')
		buf = append(buf, ':')

		switch v := fieldValues[i].(type) {
		case int, int8, int16, int32, int64:
			buf = append(buf, fmt.Sprintf("%d", v)...)
		case uint, uint8, uint16, uint32, uint64:
			buf = append(buf, fmt.Sprintf("%d", v)...)
		case float32:
			buf = append(buf, strconv.FormatFloat(float64(v), 'f', -1, 32)...)
		case float64:
			buf = append(buf, strconv.FormatFloat(v, 'f', -1, 64)...)
		case bool:
			if v {
				buf = append(buf, "true"...)
			} else {
				buf = append(buf, "false"...)
			}
		case string:
			buf = append(buf, '"')
			escaped := strings.ReplaceAll(v, `"`, `\"`)
			buf = append(buf, escaped...)
			buf = append(buf, '"')
		case []byte:
			buf = append(buf, '"')
			escaped := strings.ReplaceAll(string(v), `"`, `\"`)
			buf = append(buf, escaped...)
			buf = append(buf, '"')
		default:
			buf = append(buf, '"')
			buf = append(buf, fmt.Sprintf("%v", v)...)
			buf = append(buf, '"')
		}
	}
	buf = append(buf, '}')
	buf = append(buf, '"')

	buf = append(buf, '\n')

	_, err := w.Write(buf)
	return err
}
