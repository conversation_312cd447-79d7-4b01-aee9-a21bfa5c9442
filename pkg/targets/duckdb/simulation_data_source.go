package duckdb

import (
	"github.com/timescale/tsbs/pkg/data"
	"github.com/timescale/tsbs/pkg/data/usecases/common"
	"github.com/timescale/tsbs/pkg/targets"
)

type simulationDataSource struct {
	simulator common.Simulator
}

func newSimulationDataSource(simulator common.Simulator) targets.DataSource {
	return &simulationDataSource{
		simulator: simulator,
	}
}

func (d *simulationDataSource) NextItem() data.LoadedPoint {
	point := data.NewPoint()
	write := d.simulator.Next(point)
	if !write {
		return data.LoadedPoint{}
	}
	return data.NewLoadedPoint(point)
}

func (d *simulationDataSource) Headers() *common.GeneratedDataHeaders {
	return d.simulator.Headers()
}
