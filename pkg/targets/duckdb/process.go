package duckdb

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	_ "github.com/marcboeker/go-duckdb"
	"github.com/timescale/tsbs/pkg/data/usecases/common"
	"github.com/timescale/tsbs/pkg/targets"
)

const (
	insertTagsSQL = `INSERT OR IGNORE INTO tags(%s) VALUES %s`
	getTagsSQL    = `SELECT id, %s FROM tags`
	numExtraCols  = 2 // one for additional_tags, one for tags_id
)

// syncCSI is a thread-safe cache for storing tag ID mappings
type syncCSI struct {
	m     map[string]int64
	mutex *sync.RWMutex
}

func newSyncCSI() *syncCSI {
	return &syncCSI{
		m:     make(map[string]int64),
		mutex: &sync.RWMutex{},
	}
}

// globalSyncCSI is used when data is not hashed by some function to a worker consistently
var globalSyncCSI = newSyncCSI()

// processor handles the insertion of batches into DuckDB
type processor struct {
	db      *sql.DB
	opts    *LoadingOptions
	dbName  string
	headers *common.GeneratedDataHeaders
	_csi    *syncCSI
}

func newProcessor(opts *LoadingOptions, _ /* driver */, dbName string) targets.Processor {
	return &processor{
		opts:   opts,
		dbName: dbName,
	}
}

func (p *processor) Init(workerNum int, doLoad, hashWorkers bool) {
	if !doLoad {
		return
	}

	connStr := p.opts.GetConnectString(p.dbName)
	db, err := sql.Open("duckdb", connStr)
	if err != nil {
		log.Fatalf("Failed to open DuckDB connection: %v", err)
	}
	p.db = db

	if hashWorkers {
		p._csi = newSyncCSI()
	} else {
		p._csi = globalSyncCSI
	}

	p.loadExistingTagsInCache(p.db)
}

func (p *processor) ProcessBatch(b targets.Batch, doLoad bool) (uint64, uint64) {
	batches := b.(*hypertableArr)
	rowCnt := 0
	metricCnt := uint64(0)
	for tableName, rows := range batches.m {
		rowCnt += len(rows)
		if doLoad {
			start := time.Now()
			metricCnt += p.processTable(tableName, rows)

			if p.opts.LogBatches {
				now := time.Now()
				took := now.Sub(start)
				batchSize := len(rows)
				fmt.Printf("BATCH: batchsize %d row rate %f/sec (took %v)\n", batchSize, float64(batchSize)/float64(took.Seconds()), took)
			}
		}
	}
	batches.m = map[string][]*insertData{}
	batches.cnt = 0
	return metricCnt, uint64(rowCnt)
}

func (p *processor) Close() {
	if p.db != nil {
		p.db.Close()
	}
}

// loadExistingTagsInCache loads existing tags from the database into the cache
func (p *processor) loadExistingTagsInCache(db *sql.DB) {
	p._csi.mutex.Lock()
	defer p._csi.mutex.Unlock()

	if p.headers == nil || len(p.headers.TagKeys) == 0 {
		return
	}

	querySQL := fmt.Sprintf(getTagsSQL, strings.Join(p.headers.TagKeys, ", "))
	res, err := db.Query(querySQL)
	if err != nil {
		// If tags table doesn't exist yet, that's okay
		return
	}
	defer res.Close()

	for res.Next() {
		// Create slice to hold all values (id + all tag columns)
		values := make([]interface{}, len(p.headers.TagKeys)+1)
		valuePtrs := make([]interface{}, len(values))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		err := res.Scan(valuePtrs...)
		if err != nil {
			log.Printf("Error scanning tags: %v", err)
			continue
		}

		// First value is ID, second is hostname (first tag key)
		if id, ok := values[0].(int64); ok {
			if hostname, ok := values[1].(string); ok {
				p._csi.m[hostname] = id
			}
		}
	}
}

// processTable processes a batch of rows for a specific table
func (p *processor) processTable(tableName string, rows []*insertData) uint64 {
	if len(rows) == 0 {
		return 0
	}

	// Get table columns from headers
	if p.headers == nil {
		log.Fatalf("Headers not available for processing")
	}

	columns, exists := p.headers.FieldKeys[tableName]
	if !exists {
		log.Fatalf("Table %s not found in headers", tableName)
	}

	colLen := len(columns) + numExtraCols
	tagRows, dataRows, numMetrics := p.splitTagsAndMetrics(rows, colLen)

	// Check if any of these tags has yet to be inserted
	newTags := make([][]string, 0, len(rows))
	p._csi.mutex.RLock()
	for _, cols := range tagRows {
		if _, ok := p._csi.m[cols[0]]; !ok {
			newTags = append(newTags, cols)
		}
	}
	p._csi.mutex.RUnlock()

	if len(newTags) > 0 {
		p._csi.mutex.Lock()
		res := p.insertTags(p.db, newTags)
		for k, v := range res {
			p._csi.m[k] = v
		}
		p._csi.mutex.Unlock()
	}

	// Set tags_id for each data row
	p._csi.mutex.RLock()
	for i := range dataRows {
		tagKey := tagRows[i][0]
		dataRows[i][1] = p._csi.m[tagKey]
	}
	p._csi.mutex.RUnlock()

	// Insert data into the table
	p.insertData(tableName, columns, dataRows)

	return numMetrics
}

// splitTagsAndMetrics takes an array of insertData and divides the tags from data
func (p *processor) splitTagsAndMetrics(rows []*insertData, dataCols int) ([][]string, [][]interface{}, uint64) {
	tagRows := make([][]string, 0, len(rows))
	dataRows := make([][]interface{}, 0, len(rows))
	numMetrics := uint64(0)
	commonTagsLen := len(p.headers.TagKeys)

	for _, data := range rows {
		// Split the tags into individual common tags
		tags := strings.SplitN(data.tags, ",", commonTagsLen+1)
		for i := 0; i < commonTagsLen; i++ {
			tags[i] = strings.Split(tags[i], "=")[1]
		}

		var additionalTags interface{}
		if len(tags) > commonTagsLen {
			// Handle additional tags as JSON string
			tagsMap := subsystemTagsToJSON(strings.Split(tags[commonTagsLen], ","))
			if jsonBytes, err := json.Marshal(tagsMap); err == nil {
				additionalTags = string(jsonBytes)
			} else {
				additionalTags = nil
			}
		}

		metrics := strings.Split(data.fields, ",")
		numMetrics += uint64(len(metrics) - 1) // 1 field is timestamp

		timeInt, err := strconv.ParseInt(metrics[0], 10, 64)
		if err != nil {
			panic(err)
		}
		ts := time.Unix(0, timeInt)

		// Create data row: [time, tags_id, additional_tags, field1, field2, ...]
		r := make([]interface{}, 3, dataCols)
		r[0], r[1], r[2] = ts, nil, additionalTags // tags_id will be set later

		for _, v := range metrics[1:] {
			if v == "" {
				r = append(r, nil)
				continue
			}

			num, err := strconv.ParseFloat(v, 64)
			if err != nil {
				panic(err)
			}
			r = append(r, num)
		}

		dataRows = append(dataRows, r)
		tagRows = append(tagRows, tags[:commonTagsLen])
	}

	return tagRows, dataRows, numMetrics
}

// subsystemTagsToJSON converts additional tags to JSON format
func subsystemTagsToJSON(tags []string) map[string]interface{} {
	jsonToReturn := map[string]interface{}{}
	for _, t := range tags {
		args := strings.Split(t, "=")
		if len(args) == 2 {
			jsonToReturn[args[0]] = args[1]
		}
	}
	return jsonToReturn
}

// insertTags inserts new tags into the tags table and returns their IDs
func (p *processor) insertTags(db *sql.DB, tagRows [][]string) map[string]int64 {
	if len(tagRows) == 0 {
		return make(map[string]int64)
	}

	tagCols := p.headers.TagKeys
	ret := make(map[string]int64)

	tx, err := db.Begin()
	if err != nil {
		panic(err)
	}
	defer tx.Rollback()

	// Insert each tag row individually to handle duplicates
	for _, row := range tagRows {
		// First check if this tag combination already exists
		whereClause := make([]string, len(tagCols))
		for i, col := range tagCols {
			whereClause[i] = fmt.Sprintf("%s = %s", col, convertValToSQL(row[i], p.headers.TagTypes[i]))
		}

		querySQL := fmt.Sprintf("SELECT id FROM tags WHERE %s", strings.Join(whereClause, " AND "))
		var id int64
		err := tx.QueryRow(querySQL).Scan(&id)
		if err == nil {
			// Tag already exists
			ret[row[0]] = id
			continue
		}

		// Tag doesn't exist, insert it
		sqlValues := convertValsToSQLBasedOnType(row, p.headers.TagTypes)
		insertSQL := fmt.Sprintf("INSERT INTO tags(%s) VALUES (%s)",
			strings.Join(tagCols, ","), strings.Join(sqlValues, ","))

		_, err = tx.Exec(insertSQL)
		if err != nil {
			log.Printf("Error inserting tag: %v", err)
			continue
		}

		// Get the ID of the inserted tag
		err = tx.QueryRow(querySQL).Scan(&id)
		if err != nil {
			log.Printf("Error getting inserted tag ID: %v", err)
			continue
		}
		ret[row[0]] = id
	}

	err = tx.Commit()
	if err != nil {
		panic(err)
	}

	return ret
}

// insertData inserts data rows into the specified table
func (p *processor) insertData(tableName string, columns []string, dataRows [][]interface{}) {
	if len(dataRows) == 0 {
		return
	}

	// Build column list
	cols := []string{"time", "tags_id", "additional_tags"}
	cols = append(cols, columns...)

	// Build insert statement
	placeholders := make([]string, len(cols))
	for i := range placeholders {
		placeholders[i] = "?"
	}

	insertSQL := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		tableName, strings.Join(cols, ","), strings.Join(placeholders, ","))

	tx, err := p.db.Begin()
	if err != nil {
		panic(err)
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(insertSQL)
	if err != nil {
		panic(err)
	}
	defer stmt.Close()

	for _, row := range dataRows {
		_, err := stmt.Exec(row...)
		if err != nil {
			log.Printf("Error inserting data: %v", err)
		}
	}

	err = tx.Commit()
	if err != nil {
		panic(err)
	}
}

// convertValsToSQLBasedOnType converts values to SQL format based on their types
func convertValsToSQLBasedOnType(values []string, types []string) []string {
	sqlVals := make([]string, len(values))
	for i, val := range values {
		sqlVals[i] = convertValToSQL(val, types[i])
	}
	return sqlVals
}

// convertValToSQL converts a single value to SQL format based on its type
func convertValToSQL(val, valType string) string {
	if val == "" {
		return "NULL"
	}
	switch valType {
	case "string":
		return "'" + strings.ReplaceAll(val, "'", "''") + "'"
	default:
		return val
	}
}
