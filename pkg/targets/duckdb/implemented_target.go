package duckdb

import (
	"time"

	"github.com/blagojts/viper"
	"github.com/spf13/pflag"
	"github.com/timescale/tsbs/pkg/data/serialize"
	"github.com/timescale/tsbs/pkg/data/source"
	"github.com/timescale/tsbs/pkg/targets"
	"github.com/timescale/tsbs/pkg/targets/constants"
)

func NewTarget() targets.ImplementedTarget {
	return &duckdbTarget{}
}

type duckdbTarget struct {
}

func (t *duckdbTarget) TargetName() string {
	return constants.FormatDuckDB
}

func (t *duckdbTarget) Serializer() serialize.PointSerializer {
	return &Serializer{}
}

func (t *duckdbTarget) Benchmark(
	targetDB string, dataSourceConfig *source.DataSourceConfig, v *viper.Viper,
) (targets.Benchmark, error) {
	var loadingOptions LoadingOptions
	if err := v.Unmarshal(&loadingOptions); err != nil {
		return nil, err
	}
	return NewBenchmark(targetDB, &loadingOptions, dataSourceConfig)
}

func (t *duckdbTarget) TargetSpecificFlags(flagPrefix string, flagSet *pflag.FlagSet) {
	flagSet.String(flagPrefix+"host", "localhost", "Hostname of DuckDB instance")
	flagSet.String(flagPrefix+"port", "5432", "Which port to connect to on the database host")
	flagSet.String(flagPrefix+"user", "duckdb", "User to connect to DuckDB as")
	flagSet.String(flagPrefix+"pass", "", "Password for user connecting to DuckDB (leave blank if not password protected)")

	flagSet.Bool(flagPrefix+"log-batches", false, "Whether to time individual batches.")

	flagSet.Bool(flagPrefix+"use-jsonb-tags", false, "Whether tags should be stored as JSONB (instead of a separate table with schema)")
	flagSet.Bool(flagPrefix+"in-table-partition-tag", false, "Whether the partition key (e.g. hostname) should also be in the metrics table")

	flagSet.Int(flagPrefix+"partitions", 0, "Number of partitions")
	flagSet.Duration(flagPrefix+"chunk-time", 12*time.Hour, "Duration that each chunk should represent, e.g., 12h")

	flagSet.String(flagPrefix+"write-profile", "", "File to output CPU/memory profile to")
	flagSet.Bool(flagPrefix+"create-metrics-table", true, "Drops existing and creates new metrics table")

	flagSet.Bool(flagPrefix+"force-text-format", false, "Send/receive data in text format")
}
