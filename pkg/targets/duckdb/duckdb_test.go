package duckdb

import (
	"testing"
	"time"

	"github.com/timescale/tsbs/pkg/data"
	"github.com/timescale/tsbs/pkg/targets/constants"
)

func TestNewTarget(t *testing.T) {
	target := NewTarget()
	if target == nil {
		t.Fatal("NewTarget() returned nil")
	}

	if target.TargetName() != constants.FormatDuckDB {
		t.<PERSON>rf("Expected target name %s, got %s", constants.FormatDuckDB, target.TargetName())
	}
}

func TestSerializer(t *testing.T) {
	target := NewTarget()
	serializer := target.Serializer()
	if serializer == nil {
		t.Fatal("Serializer() returned nil")
	}

	// Test serialization with a simple point
	point := data.NewPoint()
	point.SetMeasurementName([]byte("cpu"))
	now := time.Now()
	point.SetTimestamp(&now)

	// Add some tags
	point.AppendTag([]byte("hostname"), "server01")
	point.AppendTag([]byte("region"), "us-west")

	// Add some fields
	point.AppendField([]byte("usage_user"), float64(85.5))
	point.AppendField([]byte("usage_system"), float64(12.3))

	// Test serialization doesn't panic
	var buf []byte
	err := serializer.Serialize(point, &testWriter{buf: &buf})
	if err != nil {
		t.Errorf("Serialization failed: %v", err)
	}

	if len(buf) == 0 {
		t.Error("Serialization produced empty output")
	}
}

func TestLoadingOptions(t *testing.T) {
	opts := &LoadingOptions{
		Host: "localhost",
		Port: "5432",
		User: "testuser",
	}

	connStr := opts.GetConnectString("test.db")
	if connStr != "test.db" {
		t.Errorf("Expected connection string 'test.db', got '%s'", connStr)
	}

	// Test with empty database name
	connStr = opts.GetConnectString("")
	if connStr != "tsbs_benchmark.db" {
		t.Errorf("Expected default connection string 'tsbs_benchmark.db', got '%s'", connStr)
	}
}

func TestBatchFactory(t *testing.T) {
	factory := &factory{}
	batch := factory.New()
	if batch == nil {
		t.Fatal("BatchFactory.New() returned nil")
	}

	if batch.Len() != 0 {
		t.Errorf("Expected empty batch length 0, got %d", batch.Len())
	}

	// Test appending to batch
	point := data.NewPoint()
	point.SetMeasurementName([]byte("cpu"))
	now := time.Now()
	point.SetTimestamp(&now)

	loadedPoint := data.NewLoadedPoint(point)
	batch.Append(loadedPoint)

	if batch.Len() != 1 {
		t.Errorf("Expected batch length 1 after append, got %d", batch.Len())
	}
}

func TestHostnameIndexer(t *testing.T) {
	indexer := &hostnameIndexer{partitions: 4}

	// Create a point with hostname tag
	point := data.NewPoint()
	point.SetMeasurementName([]byte("cpu"))
	now := time.Now()
	point.SetTimestamp(&now)
	point.AppendTag([]byte("hostname"), "server01")

	loadedPoint := data.NewLoadedPoint(point)
	index := indexer.GetIndex(loadedPoint)

	if index >= 4 {
		t.Errorf("Expected index < 4, got %d", index)
	}

	// Test with same hostname should give same index
	index2 := indexer.GetIndex(loadedPoint)
	if index != index2 {
		t.Errorf("Expected consistent indexing, got %d and %d", index, index2)
	}
}

// testWriter is a simple writer for testing
type testWriter struct {
	buf *[]byte
}

func (w *testWriter) Write(p []byte) (n int, err error) {
	*w.buf = append(*w.buf, p...)
	return len(p), nil
}
