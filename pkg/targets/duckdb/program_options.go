package duckdb

import (
	"time"
)

// LoadingOptions holds configuration for DuckDB loading
type LoadingOptions struct {
	Host string `mapstructure:"host"`
	Port string `mapstructure:"port"`
	User string `mapstructure:"user"`
	Pass string `mapstructure:"pass"`

	LogBatches bool `mapstructure:"log-batches"`

	UseJSONBTags        bool `mapstructure:"use-jsonb-tags"`
	InTablePartitionTag bool `mapstructure:"in-table-partition-tag"`

	Partitions int           `mapstructure:"partitions"`
	ChunkTime  time.Duration `mapstructure:"chunk-time"`

	ProfileFile string `mapstructure:"write-profile"`

	CreateMetricsTable bool `mapstructure:"create-metrics-table"`

	ForceTextFormat bool `mapstructure:"force-text-format"`
}

// GetConnectString returns the connection string for DuckDB
func (opts *LoadingOptions) GetConnectString(dbName string) string {
	// For DuckDB, we can use file-based databases or in-memory
	// For simplicity, we'll use file-based databases
	if dbName == "" {
		dbName = "tsbs_benchmark.db"
	}
	return dbName
}
