# DuckDB Support in TSBS

本文档介绍如何在 TSBS (Time Series Benchmark Suite) 中使用 DuckDB 进行时间序列数据基准测试。

## 概述

DuckDB 是一个嵌入式分析数据库，专为 OLAP 工作负载设计。它支持标准 SQL 语法，具有出色的分析性能，特别适合时间序列数据分析。

## 安装依赖

在使用 DuckDB 支持之前，需要安装 DuckDB Go 驱动程序：

```bash
go get github.com/marcboeker/go-duckdb
```

## 数据加载

使用 `tsbs_load_duckdb` 命令将时间序列数据加载到 DuckDB 中：

```bash
# 生成数据
tsbs_generate_data --use-case="devops" --seed=123 --scale=10 \
    --timestamp-start="2016-01-01T00:00:00Z" \
    --timestamp-end="2016-01-02T00:00:00Z" \
    --log-interval="10s" --format="duckdb" \
    > /tmp/duckdb-data.txt

# 加载数据到 DuckDB
tsbs_load_duckdb --file=/tmp/duckdb-data.txt \
    --db-name="benchmark.db" \
    --workers=4 \
    --batch-size=10000
```

### 加载选项

- `--host`: DuckDB 主机地址 (默认: localhost)
- `--port`: 端口号 (默认: 5432)
- `--user`: 用户名 (默认: duckdb)
- `--pass`: 密码 (默认: 空)
- `--db-name`: 数据库文件名
- `--workers`: 并发工作线程数
- `--batch-size`: 批处理大小
- `--log-batches`: 是否记录批处理时间
- `--create-metrics-table`: 是否创建新的 metrics 表 (默认: true)

## 查询生成

生成 DuckDB 特定的查询：

```bash
# 生成 devops 用例查询
tsbs_generate_queries --use-case="devops" --seed=123 --scale=10 \
    --timestamp-start="2016-01-01T00:00:00Z" \
    --timestamp-end="2016-01-02T00:00:00Z" \
    --queries=1000 --query-type="single-groupby-1-1-1" \
    --format="duckdb" \
    > /tmp/duckdb-queries.txt

# 生成 IoT 用例查询
tsbs_generate_queries --use-case="iot" --seed=123 --scale=10 \
    --timestamp-start="2016-01-01T00:00:00Z" \
    --timestamp-end="2016-01-02T00:00:00Z" \
    --queries=1000 --query-type="last-loc" \
    --format="duckdb" \
    > /tmp/duckdb-iot-queries.txt
```

### 支持的查询类型

#### DevOps 用例
- `single-groupby-1-1-1`: 单个 GROUP BY 查询
- `single-groupby-1-1-12`: 12小时窗口 GROUP BY 查询
- `single-groupby-1-8-1`: 8个指标的 GROUP BY 查询
- `single-groupby-5-1-1`: 5个主机的 GROUP BY 查询
- `single-groupby-5-1-12`: 5个主机12小时窗口查询
- `single-groupby-5-8-1`: 5个主机8个指标查询
- `double-groupby-1`: 双重 GROUP BY 查询
- `double-groupby-5`: 5个主机双重 GROUP BY 查询
- `double-groupby-all`: 所有主机双重 GROUP BY 查询
- `high-cpu-all`: 高 CPU 使用率查询
- `high-cpu-1`: 单主机高 CPU 查询
- `lastpoint`: 最后数据点查询

#### IoT 用例
- `last-loc`: 最后位置查询
- `low-fuel`: 低燃料查询
- `high-load`: 高负载查询
- `stationary-trucks`: 静止卡车查询
- `long-driving-sessions`: 长时间驾驶查询
- `long-daily-sessions`: 长时间日常驾驶查询
- `avg-vs-projected-fuel-consumption`: 平均与预期燃料消耗对比
- `avg-daily-driving-duration`: 平均日常驾驶时长
- `avg-daily-driving-session`: 平均日常驾驶会话
- `avg-load`: 平均负载
- `daily-activity`: 日常活动
- `breakdown-frequency`: 故障频率

## 查询执行

执行生成的查询并测量性能：

```bash
tsbs_run_queries_duckdb --file=/tmp/duckdb-queries.txt \
    --hosts="benchmark.db" \
    --workers=4 \
    --print-interval=100
```

### 执行选项

- `--hosts`: DuckDB 数据库文件路径
- `--workers`: 并发查询工作线程数
- `--print-interval`: 打印统计信息的间隔
- `--show-explain`: 显示查询执行计划

## 数据模型

DuckDB 实现使用以下数据模型：

```sql
CREATE TABLE metrics (
    timestamp TIMESTAMP,
    measurement VARCHAR,
    tags JSON,
    fields JSON
);

-- 索引
CREATE INDEX idx_metrics_timestamp ON metrics(timestamp);
CREATE INDEX idx_metrics_measurement ON metrics(measurement);
```

- `timestamp`: 时间戳
- `measurement`: 测量名称 (如 "cpu", "mem", "disk")
- `tags`: 标签数据，存储为 JSON 格式
- `fields`: 字段数据，存储为 JSON 格式

## 性能优化建议

1. **批处理大小**: 根据内存大小调整 `--batch-size`，通常 10000-50000 效果较好
2. **并发工作线程**: 根据 CPU 核心数调整 `--workers`
3. **索引**: 确保在时间戳和测量名称上创建索引
4. **内存配置**: DuckDB 会自动管理内存，但可以通过连接字符串配置
5. **查询优化**: 利用 DuckDB 的向量化执行引擎，避免复杂的嵌套查询

## 示例完整工作流

```bash
# 1. 生成数据
tsbs_generate_data --use-case="devops" --seed=123 --scale=100 \
    --timestamp-start="2016-01-01T00:00:00Z" \
    --timestamp-end="2016-01-02T00:00:00Z" \
    --log-interval="10s" --format="duckdb" \
    > /tmp/duckdb-data.txt

# 2. 加载数据
tsbs_load_duckdb --file=/tmp/duckdb-data.txt \
    --db-name="benchmark.db" \
    --workers=8 \
    --batch-size=20000

# 3. 生成查询
tsbs_generate_queries --use-case="devops" --seed=123 --scale=100 \
    --timestamp-start="2016-01-01T00:00:00Z" \
    --timestamp-end="2016-01-02T00:00:00Z" \
    --queries=1000 --query-type="single-groupby-1-1-1" \
    --format="duckdb" \
    > /tmp/duckdb-queries.txt

# 4. 执行查询
tsbs_run_queries_duckdb --file=/tmp/duckdb-queries.txt \
    --hosts="benchmark.db" \
    --workers=4 \
    --print-interval=100
```

## 故障排除

1. **连接错误**: 确保 DuckDB Go 驱动程序已正确安装
2. **内存不足**: 减少批处理大小或并发工作线程数
3. **查询超时**: 检查查询复杂度和数据量
4. **文件权限**: 确保对数据库文件有读写权限

## 限制

1. DuckDB 是嵌入式数据库，不支持网络连接
2. 并发写入能力有限
3. 某些高级 SQL 功能可能不支持
4. 大数据集可能需要较多内存
