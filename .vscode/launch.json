{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "generate data",
            "type": "go",
            "request": "launch",
            "mode": "debug",
            "program": "${workspaceFolder}/cmd/tsbs_generate_data/main.go",
            "cwd": "${workspaceFolder}",
            "env": {},
            "args": [
                "--use-case=cpu-only",
                "--seed=123",
                "--scale=10",
                "--timestamp-start=2016-01-01T00:00:00Z",
                "--timestamp-end=2016-01-03T00:00:00Z",
                "--log-interval=10s",
                "--format=ltmdb",
                "--file=/tmp/tsbs/data_ltmdb_scale_10.dat"
            ]
        },
        {
            "name": "generate duckdb queries",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/tsbs_generate_queries",
            "args": [
                "--use-case=cpu-only",
                "--seed=123",
                "--scale=10",
                "--timestamp-start=2016-01-01T00:00:00Z",
                "--timestamp-end=2016-01-03T00:00:00Z",
                "--queries=10",
                "--query-type=high-cpu-all",
                "--format=duckdb",
                "--file=/tmp/bulk_queries/duckdb_query_high-cpu-all.sql"
            ]
        },
        {
            "name": "generate queries",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/tsbs_generate_queries",
            "args": [
                "--use-case=cpu-only",
                "--seed=123",
                "--scale=10",
                "--timestamp-start=2016-01-01T00:00:00Z",
                "--timestamp-end=2016-01-03T00:00:00Z",
                "--queries=10",
                "--query-type=single-groupby-1-8-1",
                "--format=ltmdb",
                "--ltmdb-range-scan-tags-hint=true",
                "--file=/tmp/tsbs/queries_ltmdb_scale_10_single-groupby-1-8-1.sql"
            ]
        },
        {
            "name": "load data",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/tsbs_load_ltmdb",
            "args": [
                "--file=/tmp/tsbs/data_ltmdb_scale_10.dat",
                "--batch-size=100",
                "--workers=10"
            ]
        },
        {
            "name": "run queries",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/cmd/tsbs_run_queries_ltmdb",
            "args": [
                "--file=/tmp/tsbs/queries_ltmdb_scale_10_single-groupby-1-8-1.sql",
                "--workers=1"
            ]
        }
    ]
}