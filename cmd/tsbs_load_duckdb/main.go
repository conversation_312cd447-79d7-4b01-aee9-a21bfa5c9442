// tsbs_load_duckdb loads a TSBS dataset into DuckDB.
package main

import (
	"log"

	"github.com/blagojts/viper"
	"github.com/spf13/pflag"
	"github.com/timescale/tsbs/internal/utils"
	"github.com/timescale/tsbs/load"
	"github.com/timescale/tsbs/pkg/data/source"
	"github.com/timescale/tsbs/pkg/targets/duckdb"
)

var loader load.BenchmarkRunner
var config load.BenchmarkRunnerConfig

func initProgramOptions() (*duckdb.LoadingOptions, load.BenchmarkRunner, *load.BenchmarkRunnerConfig) {
	target := duckdb.NewTarget()

	loaderConf := &load.BenchmarkRunnerConfig{}
	loaderConf.AddToFlagSet(pflag.CommandLine)

	opts := &duckdb.LoadingOptions{}
	target.TargetSpecificFlags("", pflag.CommandLine)
	pflag.Parse()

	err := utils.SetupConfigFile()
	if err != nil {
		panic(err)
	}

	if err := viper.Unmarshal(opts); err != nil {
		panic(err)
	}

	if err := viper.Unmarshal(loaderConf); err != nil {
		panic(err)
	}

	loader := load.GetBenchmarkRunner(*loaderConf)

	return opts, loader, loaderConf
}

func main() {
	opts, loader, loaderConf := initProgramOptions()

	// If specified, generate a performance profile
	if len(opts.ProfileFile) > 0 {
		go profileCPUAndMem(opts.ProfileFile)
	}

	benchmark, err := duckdb.NewBenchmark(loaderConf.DBName, opts, &source.DataSourceConfig{
		Type: source.FileDataSourceType,
		File: &source.FileDataSourceConfig{Location: loaderConf.FileName},
	})
	if err != nil {
		panic(err)
	}
	loader.RunBenchmark(benchmark)
}

// profileCPUAndMem starts CPU and memory profiling
func profileCPUAndMem(profileFile string) {
	// This is a placeholder for profiling functionality
	// You can implement CPU and memory profiling here if needed
	log.Printf("Profiling to file: %s", profileFile)
}
