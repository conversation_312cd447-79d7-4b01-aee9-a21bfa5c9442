package duckdb

import (
	"fmt"
	"strings"

	"github.com/timescale/tsbs/cmd/tsbs_generate_queries/uses/iot"
	"github.com/timescale/tsbs/pkg/query"
)

// IoT produces DuckDB-specific queries for all the iot query types.
type IoT struct {
	*BaseGenerator
	*iot.Core
}

// getTruckWhereWithNames creates WHERE conditions for truck names using tags table
func (i *IoT) getTruckWhereWithNames(names []string) string {
	nameClauses := []string{}
	for _, s := range names {
		nameClauses = append(nameClauses, fmt.Sprintf("'%s'", s))
	}
	return fmt.Sprintf("tags_id IN (SELECT id FROM tags WHERE name IN (%s))", strings.Join(nameClauses, ","))
}

// getTruckWhereString gets multiple random truck names and creates a WHERE SQL string for these names.
func (i *IoT) getTruckWhereString(nTrucks int) string {
	names, err := i.GetRandomTrucks(nTrucks)
	if err != nil {
		panic(err.Error())
	}
	return i.getTruckWhereWithNames(names)
}

// LastLocByTruck finds the truck location for nTrucks.
func (i *IoT) LastLocByTruck(qi query.Query, nTrucks int) {
	names, err := i.GetRandomTrucks(nTrucks)
	if err != nil {
		panic(err.Error())
	}
	whereNames := i.getTruckWhereWithNames(names)

	humanLabel := fmt.Sprintf("DuckDB last location by specific truck")
	humanDesc := fmt.Sprintf("%s: random %4d trucks", humanLabel, nTrucks)

	sql := fmt.Sprintf(`SELECT
		tags.name AS name,
		tags.fleet AS fleet,
		tags.driver AS driver,
		readings.latitude AS latitude,
		readings.longitude AS longitude
	FROM readings
	JOIN tags ON readings.tags_id = tags.id
	WHERE %s
		AND time = (
			SELECT MAX(time)
			FROM readings r2
			JOIN tags t2 ON r2.tags_id = t2.id
			WHERE t2.name = tags.name
		)`, whereNames)

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// LastLocPerTruck finds the last location of every truck.
func (i *IoT) LastLocPerTruck(qi query.Query) {
	humanLabel := "DuckDB last location per truck"
	humanDesc := humanLabel

	sql := `SELECT
		tags.name AS name,
		tags.fleet AS fleet,
		tags.driver AS driver,
		readings.latitude AS latitude,
		readings.longitude AS longitude
	FROM readings
	JOIN tags ON readings.tags_id = tags.id
	WHERE time = (
		SELECT MAX(time)
		FROM readings r2
		JOIN tags t2 ON r2.tags_id = t2.id
		WHERE t2.name = tags.name
	)
	ORDER BY tags.name`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// TrucksWithLowFuel finds all trucks with low fuel (less than 10%).
func (i *IoT) TrucksWithLowFuel(qi query.Query) {
	humanLabel := "DuckDB trucks with low fuel"
	humanDesc := fmt.Sprintf("%s: under 10 percent", humanLabel)

	sql := `SELECT
		tags.name AS name,
		tags.driver AS driver,
		diagnostics.fuel_state AS fuel_state
	FROM diagnostics
	JOIN tags ON diagnostics.tags_id = tags.id
	WHERE fuel_state < 0.1
		AND time = (
			SELECT MAX(time)
			FROM diagnostics d2
			JOIN tags t2 ON d2.tags_id = t2.id
			WHERE t2.name = tags.name
		)
	ORDER BY tags.name`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// TrucksWithHighLoad finds all trucks that have load over 90%.
func (i *IoT) TrucksWithHighLoad(qi query.Query) {
	humanLabel := "DuckDB trucks with high load"
	humanDesc := fmt.Sprintf("%s: over 90 percent", humanLabel)

	sql := `SELECT
		tags.name AS name,
		tags.driver AS driver,
		tags.model AS model,
		readings.load_capacity AS load_capacity,
		diagnostics.current_load AS current_load
	FROM diagnostics
	JOIN tags ON diagnostics.tags_id = tags.id
	JOIN readings ON readings.tags_id = tags.id AND readings.time = diagnostics.time
	WHERE current_load / readings.load_capacity >= 0.9
		AND diagnostics.time = (
			SELECT MAX(time)
			FROM diagnostics d2
			JOIN tags t2 ON d2.tags_id = t2.id
			WHERE t2.name = tags.name
		)
	ORDER BY tags.name`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// StationaryTrucks finds all trucks that have low average velocity in a time window.
func (i *IoT) StationaryTrucks(qi query.Query) {
	interval := i.Interval.MustRandWindow(iot.StationaryDuration)

	humanLabel := "DuckDB stationary trucks"
	humanDesc := fmt.Sprintf("%s: with low avg velocity in last 10 minutes", humanLabel)

	sql := fmt.Sprintf(`SELECT
		tags.name AS name,
		tags.driver AS driver,
		AVG(readings.velocity) AS mean_velocity
	FROM readings
	JOIN tags ON readings.tags_id = tags.id
	WHERE time >= '%s'
		AND time <= '%s'
	GROUP BY tags.name, tags.driver
	HAVING AVG(readings.velocity) < 1
	ORDER BY tags.name`,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// TrucksWithLongDrivingSessions finds all trucks that have not stopped at least 20 mins in the last 4 hours.
func (i *IoT) TrucksWithLongDrivingSessions(qi query.Query) {
	interval := i.Interval.MustRandWindow(iot.LongDrivingSessionDuration)

	humanLabel := "DuckDB trucks with longer driving sessions"
	humanDesc := fmt.Sprintf("%s: stopped less than 20 mins in 4 hour period", humanLabel)

	sql := fmt.Sprintf(`SELECT
		tags.name AS name,
		tags.driver AS driver
	FROM readings
	JOIN tags ON readings.tags_id = tags.id
	WHERE time >= '%s'
		AND time <= '%s'
	GROUP BY tags.name, tags.driver
	HAVING SUM(CASE WHEN velocity = 0 THEN 1 ELSE 0 END) < 20
	ORDER BY tags.name`,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// TrucksWithLongDailySessions finds all trucks that have driven more than 10 hours in the last 24 hours.
func (i *IoT) TrucksWithLongDailySessions(qi query.Query) {
	interval := i.Interval.MustRandWindow(iot.DailyDrivingDuration)

	humanLabel := "DuckDB trucks with longer daily sessions"
	humanDesc := fmt.Sprintf("%s: drove more than 10 hours in the last 24 hours", humanLabel)

	sql := fmt.Sprintf(`SELECT
		tags.name AS name,
		tags.driver AS driver
	FROM readings
	JOIN tags ON readings.tags_id = tags.id
	WHERE time >= '%s'
		AND time <= '%s'
	GROUP BY tags.name, tags.driver
	HAVING SUM(CASE WHEN velocity > 0 THEN 1 ELSE 0 END) > 600
	ORDER BY tags.name`,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// AvgVsProjectedFuelConsumption calculates average and projected fuel consumption per fleet.
func (i *IoT) AvgVsProjectedFuelConsumption(qi query.Query) {
	humanLabel := "DuckDB average vs projected fuel consumption per fleet"
	humanDesc := humanLabel

	sql := `SELECT
		JSON_EXTRACT(tags, '$.fleet') AS fleet,
		AVG(CAST(JSON_EXTRACT(fields, '$.fuel_consumption') AS DOUBLE)) AS avg_fuel_consumption,
		AVG(CAST(JSON_EXTRACT(fields, '$.nominal_fuel_consumption') AS DOUBLE)) AS projected_fuel_consumption
	FROM metrics
	WHERE measurement = 'diagnostics'
		AND timestamp = (
			SELECT MAX(timestamp)
			FROM metrics m2
			WHERE m2.measurement = 'diagnostics'
				AND JSON_EXTRACT(m2.tags, '$.name') = JSON_EXTRACT(metrics.tags, '$.name')
		)
	GROUP BY JSON_EXTRACT(tags, '$.fleet')
	ORDER BY fleet`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// AvgDailyDrivingDuration finds the average driving duration per driver.
func (i *IoT) AvgDailyDrivingDuration(qi query.Query) {
	start := i.Interval.Start()
	end := i.Interval.End()

	humanLabel := "DuckDB average driver driving duration per day"
	humanDesc := humanLabel

	sql := fmt.Sprintf(`SELECT
		JSON_EXTRACT(tags, '$.driver') AS driver,
		AVG(daily_duration) AS avg_daily_duration
	FROM (
		SELECT
			JSON_EXTRACT(tags, '$.driver') AS driver,
			DATE_TRUNC('day', timestamp) AS day,
			SUM(CASE WHEN CAST(JSON_EXTRACT(fields, '$.velocity') AS DOUBLE) > 0 THEN 1 ELSE 0 END) AS daily_duration
		FROM metrics
		WHERE measurement = 'readings'
			AND timestamp >= '%s'
			AND timestamp < '%s'
		GROUP BY JSON_EXTRACT(tags, '$.driver'), DATE_TRUNC('day', timestamp)
	) AS daily_driving
	GROUP BY driver
	ORDER BY driver`,
		start.Format(goTimeFmt),
		end.Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// AvgDailyDrivingSession finds the average driving session without stopping per driver per day.
func (i *IoT) AvgDailyDrivingSession(qi query.Query) {
	start := i.Interval.Start()
	end := i.Interval.End()

	humanLabel := "DuckDB average driver driving session without stopping per day"
	humanDesc := humanLabel

	sql := fmt.Sprintf(`SELECT
		JSON_EXTRACT(tags, '$.driver') AS driver,
		AVG(session_length) AS avg_session_length
	FROM (
		SELECT
			JSON_EXTRACT(tags, '$.driver') AS driver,
			DATE_TRUNC('day', timestamp) AS day,
			-- This is a simplified calculation
			COUNT(*) / NULLIF(SUM(CASE WHEN CAST(JSON_EXTRACT(fields, '$.velocity') AS DOUBLE) = 0 THEN 1 ELSE 0 END), 0) AS session_length
		FROM metrics
		WHERE measurement = 'readings'
			AND timestamp >= '%s'
			AND timestamp < '%s'
		GROUP BY JSON_EXTRACT(tags, '$.driver'), DATE_TRUNC('day', timestamp)
	) AS daily_sessions
	WHERE session_length IS NOT NULL
	GROUP BY driver
	ORDER BY driver`,
		start.Format(goTimeFmt),
		end.Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// AvgLoad finds the average load per truck model per fleet.
func (i *IoT) AvgLoad(qi query.Query) {
	humanLabel := "DuckDB average load per truck model per fleet"
	humanDesc := humanLabel

	sql := `SELECT
		JSON_EXTRACT(tags, '$.fleet') AS fleet,
		JSON_EXTRACT(tags, '$.model') AS model,
		AVG(CAST(JSON_EXTRACT(fields, '$.current_load') AS DOUBLE)) AS avg_load
	FROM metrics
	WHERE measurement = 'diagnostics'
		AND timestamp = (
			SELECT MAX(timestamp)
			FROM metrics m2
			WHERE m2.measurement = 'diagnostics'
				AND JSON_EXTRACT(m2.tags, '$.name') = JSON_EXTRACT(metrics.tags, '$.name')
		)
	GROUP BY JSON_EXTRACT(tags, '$.fleet'), JSON_EXTRACT(tags, '$.model')
	ORDER BY fleet, model`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// DailyTruckActivity calculates the number of hours trucks has been active (non-zero velocity) per day per fleet per model.
func (i *IoT) DailyTruckActivity(qi query.Query) {
	start := i.Interval.Start()
	end := i.Interval.End()

	humanLabel := "DuckDB daily truck activity per fleet per model"
	humanDesc := humanLabel

	sql := fmt.Sprintf(`SELECT
		JSON_EXTRACT(tags, '$.fleet') AS fleet,
		JSON_EXTRACT(tags, '$.model') AS model,
		DATE_TRUNC('day', timestamp) AS day,
		SUM(CASE WHEN CAST(JSON_EXTRACT(fields, '$.velocity') AS DOUBLE) > 0 THEN 1 ELSE 0 END) AS active_time
	FROM metrics
	WHERE measurement = 'readings'
		AND timestamp >= '%s'
		AND timestamp < '%s'
	GROUP BY JSON_EXTRACT(tags, '$.fleet'), JSON_EXTRACT(tags, '$.model'), DATE_TRUNC('day', timestamp)
	ORDER BY fleet, model, day`,
		start.Format(goTimeFmt),
		end.Format(goTimeFmt))

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// TruckBreakdownFrequency calculates the breakdown frequency per truck model.
func (i *IoT) TruckBreakdownFrequency(qi query.Query) {
	humanLabel := "DuckDB truck breakdown frequency per model"
	humanDesc := humanLabel

	sql := `SELECT
		JSON_EXTRACT(tags, '$.model') AS model,
		SUM(CASE WHEN JSON_EXTRACT(fields, '$.status') = 'breakdown' THEN 1 ELSE 0 END) AS breakdown_frequency
	FROM metrics
	WHERE measurement = 'diagnostics'
	GROUP BY JSON_EXTRACT(tags, '$.model')
	ORDER BY breakdown_frequency DESC, model`

	i.fillInQuery(qi, humanLabel, humanDesc, sql)
}
