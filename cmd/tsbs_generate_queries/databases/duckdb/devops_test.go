package duckdb

import (
	"strings"
	"testing"
	"time"

	"github.com/timescale/tsbs/cmd/tsbs_generate_queries/uses/devops"
)

func TestHighCPUForHosts(t *testing.T) {
	cases := []struct {
		desc               string
		input              int
		expectedHumanLabel string
		shouldContainHost  bool
	}{
		{
			desc:               "zero hosts (all hosts)",
			input:              0,
			expectedHumanLabel: "DuckDB CPU over threshold, all hosts",
			shouldContainHost:  false,
		},
		{
			desc:               "1 host",
			input:              1,
			expectedHumanLabel: "DuckDB CPU over threshold, 1 host(s)",
			shouldContainHost:  true,
		},
		{
			desc:               "5 hosts",
			input:              5,
			expectedHumanLabel: "DuckDB CPU over threshold, 5 host(s)",
			shouldContainHost:  true,
		},
	}

	for _, c := range cases {
		t.Run(c.desc, func(t *testing.T) {
			start := time.Unix(0, 0)
			end := start.Add(devops.HighCPUDuration).Add(time.Hour)

			d := &Devops{
				BaseGenerator: &BaseGenerator{},
				Core:          &devops.Core{},
			}

			// Initialize the core with test data
			core, err := devops.NewCore(start, end, 10)
			if err != nil {
				t.Fatalf("unexpected error creating core: %v", err)
			}
			d.Core = core

			q := d.GenerateEmptyQuery()
			d.HighCPUForHosts(q, c.input)

			if got := string(q.HumanLabelName()); got != c.expectedHumanLabel {
				t.Errorf("incorrect human label: got %s want %s", got, c.expectedHumanLabel)
			}

			sqlQuery := q.String()
			if c.shouldContainHost {
				if !strings.Contains(sqlQuery, "hostname") {
					t.Errorf("expected query to contain hostname filter, but it didn't: %s", sqlQuery)
				}
			} else {
				if strings.Contains(sqlQuery, "hostname") {
					t.Errorf("expected query to NOT contain hostname filter, but it did: %s", sqlQuery)
				}
			}
		})
	}
}


