package duckdb

import (
	"fmt"
	"strings"
	"time"

	"github.com/timescale/tsbs/cmd/tsbs_generate_queries/uses/devops"
	"github.com/timescale/tsbs/pkg/query"
)

// Devops produces DuckDB-specific queries for all the devops query types.
type Devops struct {
	*BaseGenerator
	*devops.Core
}

// getHostWhereWithHostnames creates WHERE conditions for hostnames using tags table
func (d *Devops) getHostWhereWithHostnames(hostnames []string) string {
	hostnameClauses := []string{}
	for _, s := range hostnames {
		hostnameClauses = append(hostnameClauses, fmt.Sprintf("'%s'", s))
	}
	return fmt.Sprintf("tags_id IN (SELECT id FROM tags WHERE hostname IN (%s))", strings.Join(hostnameClauses, ","))
}

// getHostWhereString gets multiple random hostnames and creates a WHERE SQL string for these hostnames.
func (d *Devops) getHostWhereString(nHosts int) string {
	hostnames, err := d.GetRandomHosts(nHosts)
	if err != nil {
		panic(err.Error())
	}
	return d.getHostWhereWithHostnames(hostnames)
}

// getSelectClausesAggMetrics builds SELECT clauses for aggregating metrics
func (d *Devops) getSelectClausesAggMetrics(agg string, metrics []string) []string {
	selectClauses := make([]string, len(metrics))
	for i, m := range metrics {
		selectClauses[i] = fmt.Sprintf("%s(%s) AS %s_%s", agg, m, agg, m)
	}
	return selectClauses
}

// GroupByTime selects the MAX for numMetrics metrics under 'cpu',
// per minute for nhosts hosts,
// e.g. in pseudo-SQL:
//
// SELECT minute, max(metric1), ..., max(metricN)
// FROM cpu
// WHERE (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// AND time >= '$HOUR_START' AND time < '$HOUR_END'
// GROUP BY minute ORDER BY minute ASC
func (d *Devops) GroupByTime(qi query.Query, nHosts, numMetrics int, timeRange time.Duration) {
	interval := d.Interval.MustRandWindow(timeRange)
	metrics, err := devops.GetCPUMetricsSlice(numMetrics)
	if err != nil {
		panic(err.Error())
	}
	selectClauses := d.getSelectClausesAggMetrics("MAX", metrics)
	whereHosts := d.getHostWhereString(nHosts)

	humanLabel := fmt.Sprintf("DuckDB %d cpu metric(s), random %4d hosts, random %s by 1m", numMetrics, nHosts, timeRange)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('minute', time) AS minute,
		%s
	FROM cpu
	WHERE %s
		AND time >= '%s'
		AND time < '%s'
	GROUP BY minute
	ORDER BY minute ASC`,
		strings.Join(selectClauses, ",\n\t\t"),
		whereHosts,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// GroupByOrderByLimit benchmarks a query that has a time WHERE clause, that groups by a truncated date, orders by that date, and takes a limit:
// SELECT date_trunc('minute', time) AS t, MAX(cpu) FROM cpu
// WHERE time < '$TIME'
// GROUP BY t ORDER BY t DESC
// LIMIT $LIMIT
func (d *Devops) GroupByOrderByLimit(qi query.Query) {
	interval := d.Interval.MustRandWindow(devops.DoubleGroupByDuration)
	where := fmt.Sprintf("time < '%s'", interval.End().Format(goTimeFmt))

	humanLabel := "DuckDB max cpu over last 5 min-intervals (random end)"
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.EndString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('minute', time) AS minute,
		MAX(usage_user) AS max_usage_user
	FROM cpu
	WHERE %s
	GROUP BY minute
	ORDER BY minute DESC
	LIMIT 5`, where)

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// GroupByTimeAndPrimaryTag selects the AVG of numMetrics metrics under 'cpu' per device per hour for a day,
// e.g. in pseudo-SQL:
//
// SELECT AVG(metric1), ..., AVG(metricN) FROM cpu
// WHERE time >= '$HOUR_START' AND time < '$HOUR_END'
// AND (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// GROUP BY hour, hostname ORDER BY hour, hostname
func (d *Devops) GroupByTimeAndPrimaryTag(qi query.Query, numMetrics int) {
	metrics, err := devops.GetCPUMetricsSlice(numMetrics)
	if err != nil {
		panic(err.Error())
	}
	interval := d.Interval.MustRandWindow(devops.DoubleGroupByDuration)
	selectClauses := d.getSelectClausesAggMetrics("AVG", metrics)

	humanLabel := devops.GetDoubleGroupByLabel("DuckDB", numMetrics)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('hour', time) AS hour,
		tags.hostname AS hostname,
		%s
	FROM cpu
	JOIN tags ON cpu.tags_id = tags.id
	WHERE time >= '%s'
		AND time < '%s'
	GROUP BY hour, tags.hostname
	ORDER BY hour, tags.hostname`,
		strings.Join(selectClauses, ",\n\t\t"),
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// MaxAllCPU selects the MAX of all metrics under 'cpu' per hour for nhosts hosts,
// e.g. in pseudo-SQL:
//
// SELECT MAX(metric1), ..., MAX(metricN)
// FROM cpu WHERE (hostname = '$HOSTNAME_1' OR ... OR hostname = '$HOSTNAME_N')
// AND time >= '$HOUR_START' AND time < '$HOUR_END'
// GROUP BY hour ORDER BY hour
func (d *Devops) MaxAllCPU(qi query.Query, nHosts int, duration time.Duration) {
	interval := d.Interval.MustRandWindow(duration)
	whereHosts := d.getHostWhereString(nHosts)
	selectClauses := d.getSelectClausesAggMetrics("MAX", devops.GetAllCPUMetrics())

	humanLabel := fmt.Sprintf("DuckDB max of all cpu metrics, random %4d hosts, random %s by 1h", nHosts, duration)
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT
		DATE_TRUNC('hour', time) AS hour,
		%s
	FROM cpu
	WHERE %s
		AND time >= '%s'
		AND time < '%s'
	GROUP BY hour
	ORDER BY hour`,
		strings.Join(selectClauses, ",\n\t\t"),
		whereHosts,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt))

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// LastPointPerHost finds the last row for every host in the dataset
func (d *Devops) LastPointPerHost(qi query.Query) {
	humanLabel := "DuckDB last row per host"
	humanDesc := humanLabel

	sql := `SELECT DISTINCT
		tags.hostname AS hostname,
		LAST(time ORDER BY time) AS last_timestamp
	FROM cpu
	JOIN tags ON cpu.tags_id = tags.id
	GROUP BY tags.hostname
	ORDER BY tags.hostname`

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}

// HighCPUForHosts populates a query that gets CPU metrics when the CPU has high usage between a time period for a number of hosts
func (d *Devops) HighCPUForHosts(qi query.Query, nHosts int) {
	interval := d.Interval.MustRandWindow(devops.HighCPUDuration)
	var hostWhereClause string
	if nHosts == 0 {
		hostWhereClause = ""
	} else {
		hostWhereClause = fmt.Sprintf("AND %s", d.getHostWhereString(nHosts))
	}

	humanLabel, err := devops.GetHighCPULabel("DuckDB", nHosts)
	if err != nil {
		panic(err.Error())
	}
	humanDesc := fmt.Sprintf("%s: %s", humanLabel, interval.StartString())

	sql := fmt.Sprintf(`SELECT *
	FROM cpu
	WHERE time >= '%s'
		AND time < '%s'
		AND usage_user > 90.0
		%s
	ORDER BY time`,
		interval.Start().Format(goTimeFmt),
		interval.End().Format(goTimeFmt),
		hostWhereClause)

	d.fillInQuery(qi, humanLabel, humanDesc, sql)
}
