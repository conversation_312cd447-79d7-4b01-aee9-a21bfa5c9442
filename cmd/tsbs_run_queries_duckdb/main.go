// tsbs_run_queries_duckdb speed tests DuckDB using requests from stdin or file
//
// It reads encoded Query objects from stdin or file, and makes concurrent requests
// to the provided DuckDB endpoint.
// This program has no knowledge of the internals of the endpoint.
package main

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/blagojts/viper"
	_ "github.com/marcboeker/go-duckdb"
	"github.com/pkg/errors"
	"github.com/spf13/pflag"
	"github.com/timescale/tsbs/internal/utils"
	"github.com/timescale/tsbs/pkg/query"
)

// Program option vars:
var (
	hostList    []string
	user        string
	pass        string
	port        string
	showExplain bool
)

// Global vars:
var (
	runner *query.BenchmarkRunner
)

// Parse args:
func init() {
	var config query.BenchmarkRunnerConfig
	config.AddToFlagSet(pflag.CommandLine)

	pflag.String("hosts", "localhost", "Comma separated list of DuckDB hosts (for multiple databases)")
	pflag.String("port", "5432", "Port for DuckDB")
	pflag.String("user", "duckdb", "User to connect to DuckDB")
	pflag.String("pass", "", "Password for DuckDB")
	pflag.Bool("show-explain", false, "Print EXPLAIN output for sample query")

	pflag.Parse()

	err := utils.SetupConfigFile()
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %s", err))
	}

	if err := viper.Unmarshal(&config); err != nil {
		panic(fmt.Errorf("unable to decode config: %s", err))
	}

	hostList = strings.Split(viper.GetString("hosts"), ",")
	user = viper.GetString("user")
	pass = viper.GetString("pass")
	port = viper.GetString("port")
	showExplain = viper.GetBool("show-explain")

	runner = query.NewBenchmarkRunner(config)
}

func main() {
	runner.Run(&query.DuckDBPool, newProcessor)
}

// newProcessor creates a new processor for DuckDB queries
func newProcessor() query.Processor {
	return &processor{}
}

// processor handles the execution of DuckDB queries
type processor struct {
	db *sql.DB
}

// Init initializes the processor with a database connection
func (p *processor) Init(workerNumber int) {
	// Use round-robin to select host
	host := hostList[workerNumber%len(hostList)]

	// For DuckDB, we typically use file-based databases
	// You might want to customize this based on your setup
	connStr := fmt.Sprintf("%s.db", host)
	if host == "localhost" || host == "" {
		connStr = "tsbs_benchmark.db"
	}

	db, err := sql.Open("duckdb", connStr)
	if err != nil {
		panic(fmt.Sprintf("Failed to open DuckDB connection: %v", err))
	}

	p.db = db
}

// ProcessQuery executes a single DuckDB query and returns statistics
func (p *processor) ProcessQuery(q query.Query, isWarm bool) ([]*query.Stat, error) {
	// Cast to DuckDB query
	duckdbQuery := q.(*query.DuckDB)

	// Prepare the query
	sql := string(duckdbQuery.SqlQuery)

	// Show EXPLAIN if requested and this is not a warm-up query
	if showExplain && !isWarm {
		explainSQL := "EXPLAIN " + sql
		rows, err := p.db.Query(explainSQL)
		if err != nil {
			return nil, errors.Wrap(err, "failed to run EXPLAIN query")
		}
		defer rows.Close()

		fmt.Printf("EXPLAIN for query: %s\n", duckdbQuery.HumanLabelName())
		for rows.Next() {
			var explain string
			if err := rows.Scan(&explain); err != nil {
				return nil, errors.Wrap(err, "failed to scan EXPLAIN result")
			}
			fmt.Printf("%s\n", explain)
		}
		fmt.Println()
	}

	// Execute the query and measure time
	start := time.Now()
	rows, err := p.db.Query(sql)
	if err != nil {
		return nil, errors.Wrap(err, "failed to execute query")
	}
	defer rows.Close()

	// Count the rows returned
	rowCount := 0
	for rows.Next() {
		rowCount++
	}

	if err := rows.Err(); err != nil {
		return nil, errors.Wrap(err, "error iterating over query results")
	}

	took := time.Since(start)

	// Create statistics
	stat := query.GetStat()
	stat.Init(duckdbQuery.HumanLabelName(), float64(took.Nanoseconds()))

	return []*query.Stat{stat}, nil
}
