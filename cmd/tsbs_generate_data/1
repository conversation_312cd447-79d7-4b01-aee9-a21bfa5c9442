WORK=/tmp/go-build2425853777
WORK=/tmp/go-build2425853777
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs
git status --porcelain
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs
git status --porcelain
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs
git -c log.showsignature=false log -1 --format=%H:%ct
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs
git -c log.showsignature=false log -1 --format=%H:%ct
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6 --
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6 --
0.002s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6 --
0.002s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6 --
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git for-each-ref --format %(refname) refs/tags --merged 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git for-each-ref --format %(refname) refs/tags --merged 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git for-each-ref --format %(refname) refs/tags --merged 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git for-each-ref --format %(refname) refs/tags --merged 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdc --
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdc --
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdc --
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git cat-file blob 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6:go.mod
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git -c log.showsignature=false log --no-decorate -n1 '--format=format:%H %ct %D' 0ea3ace7ffdc --
cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git cat-file blob 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6:go.mod
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git cat-file blob 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6:go.mod
0.001s # cd /home/<USER>/ws/go/src/github.com/timescale/tsbs; git cat-file blob 0ea3ace7ffdcbded3d249f60fd6c7727ff3c23d6:go.mod
